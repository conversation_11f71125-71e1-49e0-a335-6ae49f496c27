import ProfilePresenter from "./profile-presenter";
import { NavigationBar } from "../../components/NavigationBar.js";

export default class ProfilePage {
  constructor() {
    // Store selected file temporarily until form submission
    this.pendingProfilePicture = null;
    this.hasUnsavedChanges = false;
  }

  async render() {
    // Get user initial from localStorage
    const userName = localStorage.getItem("user_name") || "User";
    const userInitial = userName.charAt(0).toUpperCase();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userInitial,
      showProfile: true,
    });

    return `
      <div class="profile-page-container">
        ${navbar.render()}

        <div class="profile-container">
          <aside class="profile-sidebar">
            <div class="sidebar-avatar">
              <img src="images/avatar.jpg" alt="Avatar" class="avatar" id="sidebarAvatar"/>
              <p id="sidebarUsername">Username User</p>
              <p id="sidebarExperience">Experience Level</p>
              <button class="sidebar-button">Profile Pengguna</button>
            </div>
          </aside>

          <main>
            <section class="container-profile">
              <h1>Profile Pengguna</h1>
              <hr />
              <div class="profile-avatar-wrapper">
                <div class="avatar-left">
                  <img src="images/avatar.jpg" alt="Avatar" id="avatarPreview" class="avatar"/>
                </div>
                <div class="edit-right">
                  <button id="editAvatarBtn">Edit Photo</button>
                  <p class="avatar-note">Format harus berupa gambar. Tidak boleh lebih dari 2MB.</p>
                </div>
              </div>

              <div class="profile-info">
                <div class="input-group">
                  <label for="fullNameInput">Nama Lengkap:</label>
                  <input type="text" id="fullNameInput" placeholder="Enter your full name" />
                </div>

                <div class="input-group">
                  <label for="usernameInput">Username:</label>
                  <input type="text" id="usernameInput" placeholder="Enter your username" />
                </div>
              </div>

              <div class="profile-experience">
                <label>Select your farming experience level:</label>
                <div class="experience-options">
                  <label><input type="radio" name="experience" value="Beginner" /> Beginner</label>
                  <label><input type="radio" name="experience" value="Intermediate" /> Intermediate</label>
                  <label><input type="radio" name="experience" value="Experienced" /> Experienced</label>
                </div>
              </div>

              <input type="file" id="avatarInput" accept="image/*" capture="environment" style="display:none"/>

              <div class="profile-save-btn-wrapper" style="margin-top: 20px;">
                <button id="saveProfileBtn" class="btn btn-primary">Edit Profile</button>
                <div id="unsavedChangesIndicator" style="display: none; margin-top: 10px; color: #ff6b35; font-size: 14px;">
                  <i>⚠️ You have unsaved changes</i>
                </div>
              </div>
            </section>
          </main>
        </div>

        <footer class="profile-footer">
          <p>&copy; 2025 AgriEdu. All rights reserved.</p>
        </footer>
      </div>
    `;
  }

  async afterRender() {
    // Set up navigation bar events
    this.setupNavigationEvents();

    // Set up avatar preview functionality (no immediate upload)
    const editBtn = document.getElementById("editAvatarBtn");
    const avatarInput = document.getElementById("avatarInput");
    const avatarPreview = document.getElementById("avatarPreview");

    if (editBtn && avatarInput && avatarPreview) {
      editBtn.addEventListener("click", () => {
        avatarInput.click();
      });

      avatarInput.addEventListener("change", () => {
        const file = avatarInput.files[0];
        if (file) {
          // Validate file size (2MB limit as mentioned in UI)
          if (file.size > 2 * 1024 * 1024) {
            alert("File terlalu besar. Maksimal 2MB.");
            avatarInput.value = ""; // Clear the input
            return;
          }

          // Validate file type
          if (!file.type.startsWith('image/')) {
            alert("File harus berupa gambar.");
            avatarInput.value = ""; // Clear the input
            return;
          }

          // Store the file for later upload
          this.pendingProfilePicture = file;

          // Show preview of selected image
          this.showImagePreview(file);

          // Mark as having unsaved changes
          this.markAsUnsaved();

          console.log("Profile picture selected for preview:", file.name);
        }
      });
    }

    // Set up form change tracking
    this.setupFormChangeTracking();

    // Initialize ProfilePresenter
    ProfilePresenter.init(this);
  }

  setupNavigationEvents() {
    // Set up navigation bar events using the NavigationBar component's centralized event handling
    const userName = localStorage.getItem("user_name") || "User";
    const userInitial = userName.charAt(0).toUpperCase();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userInitial,
      showProfile: true,
    });

    // Use the NavigationBar's built-in event binding
    navbar.bindEvents();
  }

  /**
   * Set up tracking for form input changes
   */
  setupFormChangeTracking() {
    const formInputs = [
      document.getElementById("fullNameInput"),
      document.getElementById("usernameInput"),
      ...document.querySelectorAll('input[name="experience"]')
    ];

    formInputs.forEach(input => {
      if (input) {
        input.addEventListener('input', () => {
          if (!this.hasUnsavedChanges && !this.pendingProfilePicture) {
            this.markAsUnsaved();
          }
        });

        input.addEventListener('change', () => {
          if (!this.hasUnsavedChanges && !this.pendingProfilePicture) {
            this.markAsUnsaved();
          }
        });
      }
    });
  }

  async showProfile({ fullName, username, experience }) {
    // Update main profile form
    const fullNameInput = document.getElementById("fullNameInput");
    const usernameInput = document.getElementById("usernameInput");

    if (fullNameInput) fullNameInput.value = fullName || "";
    if (usernameInput) usernameInput.value = username || "";

    // Update experience radio buttons
    if (experience) {
      const radio = document.querySelector(
        `input[name="experience"][value="${experience}"]`
      );
      if (radio) radio.checked = true;
    }

    // Update avatar images using profile picture service for consistency
    const profilePictureService = (await import('../../data/profile-picture-service.js')).default;

    // Get user data to extract profilePictureUrl
    const authService = (await import('../../data/auth-service.js')).default;
    const userData = authService.getUserData();

    const avatarPreview = document.getElementById("avatarPreview");
    if (avatarPreview) {
      profilePictureService.updateImageElement(
        avatarPreview,
        userData?.profilePictureUrl,
        username
      );
    }

    const sidebarAvatar = document.getElementById("sidebarAvatar");
    if (sidebarAvatar) {
      profilePictureService.updateImageElement(
        sidebarAvatar,
        userData?.profilePictureUrl,
        username
      );
    }

    // Update sidebar information
    const sidebarUsername = document.getElementById("sidebarUsername");
    const sidebarExperience = document.getElementById("sidebarExperience");

    if (sidebarUsername)
      sidebarUsername.textContent = username || "Username User";
    if (sidebarExperience)
      sidebarExperience.textContent = experience || "Experience Level";
  }

  /**
   * Show preview of selected image file
   * @param {File} file - The selected image file
   */
  showImagePreview(file) {
    const reader = new FileReader();

    reader.onload = (e) => {
      const imageDataUrl = e.target.result;

      // Remove any existing fallback avatars
      const existingFallbacks = document.querySelectorAll('.fallback-avatar');
      existingFallbacks.forEach(fallback => fallback.remove());

      // Update main profile avatar preview
      const avatarPreview = document.getElementById("avatarPreview");
      if (avatarPreview) {
        avatarPreview.src = imageDataUrl;
        avatarPreview.style.display = 'block';
        avatarPreview.alt = "Profile Picture Preview";
      }

      // Update sidebar avatar preview
      const sidebarAvatar = document.getElementById("sidebarAvatar");
      if (sidebarAvatar) {
        sidebarAvatar.src = imageDataUrl;
        sidebarAvatar.style.display = 'block';
        sidebarAvatar.alt = "Profile Picture Preview";
      }

      console.log("Profile picture preview updated");
    };

    reader.onerror = () => {
      console.error("Failed to read selected image file");
      alert("Gagal membaca file gambar yang dipilih.");
    };

    reader.readAsDataURL(file);
  }

  /**
   * Mark the form as having unsaved changes
   */
  markAsUnsaved() {
    this.hasUnsavedChanges = true;

    // Show unsaved changes indicator
    const indicator = document.getElementById("unsavedChangesIndicator");
    if (indicator) {
      indicator.style.display = 'block';
    }

    // Update button text to indicate pending changes
    const saveBtn = document.getElementById("saveProfileBtn");
    if (saveBtn && this.pendingProfilePicture) {
      saveBtn.textContent = "Save Profile & Upload Picture";
    }
  }

  /**
   * Mark the form as saved (clear unsaved changes)
   */
  markAsSaved() {
    this.hasUnsavedChanges = false;
    this.pendingProfilePicture = null;

    // Hide unsaved changes indicator
    const indicator = document.getElementById("unsavedChangesIndicator");
    if (indicator) {
      indicator.style.display = 'none';
    }

    // Reset button text
    const saveBtn = document.getElementById("saveProfileBtn");
    if (saveBtn) {
      saveBtn.textContent = "Edit Profile";
    }
  }

  /**
   * Get the pending profile picture file
   * @returns {File|null} The pending profile picture file or null
   */
  getPendingProfilePicture() {
    return this.pendingProfilePicture;
  }

  /**
   * Check if there are unsaved changes
   * @returns {boolean} True if there are unsaved changes
   */
  hasUnsavedProfileChanges() {
    return this.hasUnsavedChanges;
  }
}
