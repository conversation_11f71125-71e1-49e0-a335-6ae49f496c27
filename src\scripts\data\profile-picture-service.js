import CONFIG from "../config.js";
import authService from "./auth-service.js";

class ProfilePictureService {
  /**
   * Upload a profile picture to the API
   * @param {File} file - The image file to upload
   * @returns {Promise<Object>} - Updated user data with new profile picture URL
   */
  async uploadProfilePicture(file) {
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error("Authentication required");
      }

      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append("profilePicture", file);

      const response = await fetch(
        `${CONFIG.BASE_URL}${CONFIG.API_ENDPOINTS.ACCOUNT.UPLOAD_PROFILE_PICTURE}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to upload profile picture");
      }

      const userData = await response.json();

      // Update stored user data while preserving existing tokens
      const existingToken = authService.getToken();
      const existingRefreshToken = authService.getRefreshToken();

      authService.saveAuthData({
        user: userData,
        token: existingToken,
        refreshToken: existingRefreshToken
      });

      return userData;
    } catch (error) {
      console.error("Profile picture upload error:", error);
      throw error;
    }
  }

  /**
   * Delete the current user's profile picture
   * @returns {Promise<Object>} - Updated user data without profile picture
   */
  async deleteProfilePicture() {
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(
        `${CONFIG.BASE_URL}${CONFIG.API_ENDPOINTS.ACCOUNT.DELETE_PROFILE_PICTURE}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete profile picture");
      }

      const userData = await response.json();

      // Update stored user data while preserving existing tokens
      const existingToken = authService.getToken();
      const existingRefreshToken = authService.getRefreshToken();

      authService.saveAuthData({
        user: userData,
        token: existingToken,
        refreshToken: existingRefreshToken
      });

      return userData;
    } catch (error) {
      console.error("Profile picture delete error:", error);
      throw error;
    }
  }

  /**
   * Fetch a profile picture by filename (for any user)
   * @param {string} filename - The profile picture filename
   * @returns {Promise<Blob>} - The image blob data
   */
  async fetchProfilePictureByFilename(filename) {
    try {
      if (!filename) {
        throw new Error("Filename is required");
      }

      const response = await fetch(
        `${CONFIG.BASE_URL}/api/account/profile-picture/${filename}`,
        {
          method: "GET",
          // No authentication required for public profile pictures
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch profile picture: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error("Profile picture fetch error:", error);
      throw error;
    }
  }

  /**
   * Get the full URL for a profile picture
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @returns {string|null} - Full URL to the profile picture or null if none
   */
  getProfilePictureUrl(profilePictureUrl) {
    if (!profilePictureUrl) {
      return null;
    }

    // If it's already a full URL, return as is
    if (profilePictureUrl.startsWith("http://") || profilePictureUrl.startsWith("https://")) {
      return profilePictureUrl;
    }

    // Extract filename from the URL if it contains the full path
    const filename = profilePictureUrl.split('/').pop();

    // Construct full URL using the API schema
    return `${CONFIG.BASE_URL}/api/account/profile-picture/${filename}`;
  }

  /**
   * Check if a profile picture URL is accessible
   * @param {string} profilePictureUrl - The profile picture URL to check
   * @returns {Promise<boolean>} - True if accessible, false otherwise
   */
  async isProfilePictureAccessible(profilePictureUrl) {
    try {
      const fullUrl = this.getProfilePictureUrl(profilePictureUrl);
      if (!fullUrl) {
        return false;
      }

      const response = await fetch(fullUrl, { method: "HEAD" });
      return response.ok;
    } catch (error) {
      console.warn("Profile picture accessibility check failed:", error);
      return false;
    }
  }

  /**
   * Create a fallback avatar element with user's initial
   * @param {string} username - User's username
   * @param {string} size - Size class ('small', 'medium', 'large')
   * @returns {string} - HTML string for fallback avatar
   */
  createFallbackAvatar(username, size = 'medium') {
    const initial = (username || 'U').charAt(0).toUpperCase();
    const sizeClasses = {
      small: 'width: 40px; height: 40px; font-size: 16px;',
      medium: 'width: 80px; height: 80px; font-size: 24px;',
      large: 'width: 120px; height: 120px; font-size: 36px;'
    };

    return `
      <div class="author-avatar fallback-avatar ${size}" style="
        ${sizeClasses[size]}
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex; align-items: center; justify-content: center;
        color: white; font-weight: bold;
      ">
        ${initial}
      </div>
    `;
  }

  /**
   * Create a complete profile picture element (img or fallback)
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @param {string} username - User's username for fallback
   * @param {string} size - Size class ('small', 'medium', 'large')
   * @param {string} additionalClasses - Additional CSS classes to apply
   * @returns {string} - HTML string for profile picture element
   */
  createProfilePictureElement(profilePictureUrl, username, size = 'medium', additionalClasses = '') {
    const fullUrl = this.getProfilePictureUrl(profilePictureUrl);
    const classes = `author-avatar ${size} ${additionalClasses}`.trim();

    if (fullUrl) {
      return `<img src="${fullUrl}" alt="${username || 'User'} Profile Picture" class="${classes}" onerror="this.onerror=null; this.style.display='none'; this.parentNode.innerHTML += '${this.createFallbackAvatar(username, size).replace(/'/g, '\\\'')}';">`;
    } else {
      return this.createFallbackAvatar(username, size);
    }
  }

  /**
   * Update an image element with profile picture or fallback
   * @param {HTMLImageElement} imgElement - The image element to update
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @param {string} username - User's username for fallback
   */
  updateImageElement(imgElement, profilePictureUrl, username) {
    if (!imgElement) return;

    // Remove any existing fallback avatars first
    const existingFallbacks = imgElement.parentNode.querySelectorAll('.fallback-avatar');
    existingFallbacks.forEach(fallback => fallback.remove());

    const fullUrl = this.getProfilePictureUrl(profilePictureUrl);

    if (fullUrl) {
      // Clear any previous error handlers
      imgElement.onerror = null;

      imgElement.src = fullUrl;
      imgElement.alt = `${username || 'User'} Profile Picture`;
      imgElement.style.display = 'block';

      // Set up error handling for failed image loads
      imgElement.onerror = () => {
        console.warn("Failed to load profile picture, using fallback");
        imgElement.style.display = 'none';

        // Create and insert fallback avatar
        const fallbackHtml = this.createFallbackAvatar(username);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = fallbackHtml;
        const fallbackElement = tempDiv.firstElementChild;

        imgElement.parentNode.insertBefore(fallbackElement, imgElement);
      };

      // Force image reload to trigger load/error events
      const currentSrc = imgElement.src;
      imgElement.src = '';
      imgElement.src = currentSrc;
    } else {
      // No profile picture available, hide image and show fallback
      imgElement.style.display = 'none';
      imgElement.onerror = null;

      const fallbackHtml = this.createFallbackAvatar(username);
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = fallbackHtml;
      const fallbackElement = tempDiv.firstElementChild;

      imgElement.parentNode.insertBefore(fallbackElement, imgElement);
    }
  }

  /**
   * Update a container element with profile picture or fallback
   * @param {HTMLElement} containerElement - The container element to update
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @param {string} username - User's username for fallback
   * @param {string} size - Size class ('small', 'medium', 'large')
   */
  updateContainerElement(containerElement, profilePictureUrl, username, size = 'medium') {
    if (!containerElement) return;

    const profilePictureHtml = this.createProfilePictureElement(profilePictureUrl, username, size);
    containerElement.innerHTML = profilePictureHtml;
  }
}

export default new ProfilePictureService();
